/**
 * 预测性维护服务主应用模块
 */

import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { TerminusModule } from '@nestjs/terminus';
// import { CacheModule } from '@nestjs/cache-manager';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import { MaintenanceModule } from './maintenance/maintenance.module';
import { HealthModule } from './health/health.module';
import { SimpleCacheService } from './common/services/simple-cache.service';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      cache: true,
    }),

    // 数据库模块
    TypeOrmModule.forRootAsync({
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get<string>('DB_HOST', 'localhost'),
        port: configService.get<number>('DB_PORT', 3306),
        username: configService.get<string>('DB_USERNAME', 'root'),
        password: configService.get<string>('DB_PASSWORD', ''),
        database: configService.get<string>('DB_DATABASE', 'predictive_maintenance'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        synchronize: configService.get<boolean>('DB_SYNCHRONIZE', false),
        logging: configService.get<boolean>('DB_LOGGING', false),
        timezone: '+08:00',
        charset: 'utf8mb4',
        extra: {
          connectionLimit: 10,
          acquireTimeout: 60000,
          timeout: 60000,
        },
      }),
      inject: [ConfigService],
    }),

    // Redis缓存模块 (暂时禁用，避免依赖问题)
    // CacheModule.register({
    //   isGlobal: true,
    //   ttl: 300, // 5分钟
    //   max: 100, // 最大缓存数量
    // }),

    // 任务调度模块
    ScheduleModule.forRoot(),

    // 健康检查模块
    TerminusModule,
    HealthModule,

    // 业务模块
    MaintenanceModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    SimpleCacheService, // 简单缓存服务
  ],
})
export class AppModule {}
