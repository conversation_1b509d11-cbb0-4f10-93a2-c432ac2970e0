import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, LessThan } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as tf from '@tensorflow/tfjs';
import * as ss from 'simple-statistics';
import * as moment from 'moment';

/**
 * 设备健康状态枚举
 */
export enum HealthStatus {
  EXCELLENT = 'excellent',
  GOOD = 'good',
  FAIR = 'fair',
  POOR = 'poor',
  CRITICAL = 'critical'
}

/**
 * 故障类型枚举
 */
export enum FailureType {
  MECHANICAL = 'mechanical',
  ELECTRICAL = 'electrical',
  HYDRAULIC = 'hydraulic',
  PNEUMATIC = 'pneumatic',
  SOFTWARE = 'software',
  SENSOR = 'sensor',
  WEAR = 'wear',
  OVERHEATING = 'overheating'
}

/**
 * 维护类型枚举
 */
export enum MaintenanceType {
  PREVENTIVE = 'preventive',
  PREDICTIVE = 'predictive',
  CORRECTIVE = 'corrective',
  EMERGENCY = 'emergency'
}

/**
 * 设备健康数据接口
 */
export interface DeviceHealthData {
  deviceId: string;
  timestamp: Date;
  temperature: number;
  vibration: number;
  pressure: number;
  current: number;
  voltage: number;
  speed: number;
  torque: number;
  efficiency: number;
  operatingHours: number;
  cycleCount: number;
}

/**
 * 故障预测结果接口
 */
export interface FailurePrediction {
  deviceId: string;
  failureType: FailureType;
  probability: number;
  timeToFailure: number; // 小时
  confidence: number;
  contributingFactors: string[];
  recommendedActions: string[];
  severity: 'low' | 'medium' | 'high';
  timestamp: Date;
}

/**
 * 维护建议接口
 */
export interface MaintenanceRecommendation {
  deviceId: string;
  maintenanceType: MaintenanceType;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  description: string;
  estimatedDuration: number; // 小时
  estimatedCost: number;
  requiredSkills: string[];
  requiredParts: string[];
  scheduledDate: Date;
  deadline: Date;
  riskIfDeferred: string;
}

/**
 * 设备健康评分接口
 */
export interface HealthScore {
  deviceId: string;
  overallScore: number;
  status: HealthStatus;
  componentScores: {
    mechanical: number;
    electrical: number;
    thermal: number;
    vibration: number;
    performance: number;
  };
  trends: {
    shortTerm: 'improving' | 'stable' | 'degrading';
    longTerm: 'improving' | 'stable' | 'degrading';
  };
  lastUpdated: Date;
}

/**
 * 预测性维护服务
 */
@Injectable()
export class MaintenanceService {
  private readonly logger = new Logger(MaintenanceService.name);
  
  // AI模型缓存
  private models: Map<string, tf.LayersModel> = new Map();
  private modelTrainingData: Map<string, DeviceHealthData[]> = new Map();
  
  // 健康评分缓存
  private healthScores: Map<string, HealthScore> = new Map();
  private predictions: Map<string, FailurePrediction[]> = new Map();

  constructor() {
    // 初始化AI模型 (暂时禁用以测试服务启动)
    // this.initializeModels();

    // 启动定期分析任务
    this.startPeriodicAnalysis();

    this.logger.log('预测性维护服务已初始化 (AI模型暂时禁用)');
  }

  /**
   * 分析设备健康状态
   * @param deviceId 设备ID
   * @param timeRange 时间范围
   * @returns 健康评分
   */
  async analyzeDeviceHealth(deviceId: string, timeRange?: { start: Date; end: Date }): Promise<HealthScore> {
    try {
      // 获取设备健康数据
      const healthData = await this.getDeviceHealthData(deviceId, timeRange);
      
      if (healthData.length === 0) {
        throw new Error(`设备 ${deviceId} 没有健康数据`);
      }

      // 计算各组件健康评分
      const componentScores = this.calculateComponentScores(healthData);
      
      // 计算总体健康评分
      const overallScore = this.calculateOverallHealthScore(componentScores);
      
      // 确定健康状态
      const status = this.determineHealthStatus(overallScore);
      
      // 分析趋势
      const trends = await this.analyzeTrends(deviceId, healthData);

      const healthScore: HealthScore = {
        deviceId,
        overallScore,
        status,
        componentScores,
        trends,
        lastUpdated: new Date()
      };

      // 缓存结果
      this.healthScores.set(deviceId, healthScore);

      this.logger.log(`设备健康分析完成: ${deviceId} - 评分: ${overallScore} (${status})`);
      return healthScore;

    } catch (error) {
      this.logger.error(`设备健康分析失败: ${deviceId}`, error);
      throw error;
    }
  }

  /**
   * 预测设备故障
   * @param deviceId 设备ID
   * @param predictionHorizon 预测时间范围（小时）
   * @returns 故障预测结果
   */
  async predictFailures(deviceId: string, predictionHorizon: number = 168): Promise<FailurePrediction[]> {
    try {
      // 获取设备历史数据
      const endDate = new Date();
      const startDate = moment(endDate).subtract(30, 'days').toDate();
      const healthData = await this.getDeviceHealthData(deviceId, { start: startDate, end: endDate });

      if (healthData.length < 100) {
        throw new Error(`设备 ${deviceId} 历史数据不足，无法进行故障预测`);
      }

      // 使用AI模型进行预测
      const predictions = await this.runFailurePredictionModel(deviceId, healthData, predictionHorizon);
      
      // 缓存预测结果
      this.predictions.set(deviceId, predictions);

      this.logger.log(`故障预测完成: ${deviceId} - 发现 ${predictions.length} 个潜在故障`);
      return predictions;

    } catch (error) {
      this.logger.error(`故障预测失败: ${deviceId}`, error);
      throw error;
    }
  }

  /**
   * 生成维护建议
   * @param deviceId 设备ID
   * @param includePreventive 是否包含预防性维护
   * @returns 维护建议列表
   */
  async generateMaintenanceRecommendations(
    deviceId: string, 
    includePreventive: boolean = true
  ): Promise<MaintenanceRecommendation[]> {
    try {
      const recommendations: MaintenanceRecommendation[] = [];

      // 获取设备健康状态
      const healthScore = await this.analyzeDeviceHealth(deviceId);
      
      // 获取故障预测
      const predictions = await this.predictFailures(deviceId);

      // 基于健康状态生成建议
      if (healthScore.status === HealthStatus.CRITICAL || healthScore.status === HealthStatus.POOR) {
        recommendations.push(...this.generateCriticalMaintenanceRecommendations(deviceId, healthScore));
      }

      // 基于故障预测生成建议
      const highRiskPredictions = predictions.filter(p => p.probability > 0.7 || p.severity === 'high');
      for (const prediction of highRiskPredictions) {
        recommendations.push(...this.generatePredictiveMaintenanceRecommendations(prediction));
      }

      // 生成预防性维护建议
      if (includePreventive) {
        recommendations.push(...await this.generatePreventiveMaintenanceRecommendations(deviceId));
      }

      // 按优先级排序
      recommendations.sort((a, b) => {
        const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      });

      this.logger.log(`维护建议生成完成: ${deviceId} - ${recommendations.length} 条建议`);
      return recommendations;

    } catch (error) {
      this.logger.error(`生成维护建议失败: ${deviceId}`, error);
      throw error;
    }
  }

  /**
   * 计算剩余使用寿命
   * @param deviceId 设备ID
   * @param component 组件名称
   * @returns 剩余使用寿命（小时）
   */
  async calculateRemainingUsefulLife(deviceId: string, component?: string): Promise<number> {
    try {
      const healthData = await this.getDeviceHealthData(deviceId);
      
      if (healthData.length === 0) {
        throw new Error(`设备 ${deviceId} 没有健康数据`);
      }

      // 使用退化模型计算RUL
      const rul = await this.runRULPredictionModel(deviceId, healthData, component);

      this.logger.log(`剩余使用寿命计算完成: ${deviceId} - ${rul} 小时`);
      return rul;

    } catch (error) {
      this.logger.error(`计算剩余使用寿命失败: ${deviceId}`, error);
      throw error;
    }
  }

  /**
   * 异常检测
   * @param deviceId 设备ID
   * @param sensitivity 敏感度
   * @returns 异常检测结果
   */
  async detectAnomalies(deviceId: string, sensitivity: number = 0.95): Promise<any[]> {
    try {
      const healthData = await this.getDeviceHealthData(deviceId);
      
      if (healthData.length < 50) {
        throw new Error(`设备 ${deviceId} 数据不足，无法进行异常检测`);
      }

      const anomalies = await this.runAnomalyDetectionModel(deviceId, healthData, sensitivity);

      this.logger.log(`异常检测完成: ${deviceId} - 发现 ${anomalies.length} 个异常`);
      return anomalies;

    } catch (error) {
      this.logger.error(`异常检测失败: ${deviceId}`, error);
      throw error;
    }
  }

  /**
   * 初始化AI模型
   */
  private async initializeModels(): Promise<void> {
    try {
      // 创建故障预测模型
      const failurePredictionModel = await this.createFailurePredictionModel();
      this.models.set('failure_prediction', failurePredictionModel);

      // 创建异常检测模型
      const anomalyDetectionModel = await this.createAnomalyDetectionModel();
      this.models.set('anomaly_detection', anomalyDetectionModel);

      // 创建RUL预测模型
      const rulPredictionModel = await this.createRULPredictionModel();
      this.models.set('rul_prediction', rulPredictionModel);

      this.logger.log('AI模型初始化完成');

    } catch (error) {
      this.logger.error('AI模型初始化失败', error);
    }
  }

  /**
   * 创建故障预测模型
   */
  private async createFailurePredictionModel(): Promise<tf.LayersModel> {
    const model = tf.sequential({
      layers: [
        tf.layers.dense({ inputShape: [12], units: 64, activation: 'relu' }),
        tf.layers.dropout({ rate: 0.2 }),
        tf.layers.dense({ units: 32, activation: 'relu' }),
        tf.layers.dropout({ rate: 0.2 }),
        tf.layers.dense({ units: 16, activation: 'relu' }),
        tf.layers.dense({ units: 8, activation: 'sigmoid' }) // 8种故障类型
      ]
    });

    model.compile({
      optimizer: 'adam',
      loss: 'binaryCrossentropy',
      metrics: ['accuracy']
    });

    return model;
  }

  /**
   * 创建异常检测模型（自编码器）
   */
  private async createAnomalyDetectionModel(): Promise<tf.LayersModel> {
    const inputLayer = tf.input({ shape: [12] });

    // 编码器
    const encoded = tf.layers.dense({ units: 8, activation: 'relu' }).apply(inputLayer) as tf.SymbolicTensor;
    const encoded2 = tf.layers.dense({ units: 4, activation: 'relu' }).apply(encoded) as tf.SymbolicTensor;

    // 解码器
    const decoded = tf.layers.dense({ units: 8, activation: 'relu' }).apply(encoded2) as tf.SymbolicTensor;
    const output = tf.layers.dense({ units: 12, activation: 'linear' }).apply(decoded) as tf.SymbolicTensor;

    const model = tf.model({ inputs: inputLayer, outputs: output });

    model.compile({
      optimizer: 'adam',
      loss: 'meanSquaredError'
    });

    return model;
  }

  /**
   * 创建RUL预测模型
   */
  private async createRULPredictionModel(): Promise<tf.LayersModel> {
    const model = tf.sequential({
      layers: [
        tf.layers.lstm({ inputShape: [10, 12], units: 50, returnSequences: true }),
        tf.layers.dropout({ rate: 0.2 }),
        tf.layers.lstm({ units: 50, returnSequences: false }),
        tf.layers.dropout({ rate: 0.2 }),
        tf.layers.dense({ units: 25, activation: 'relu' }),
        tf.layers.dense({ units: 1, activation: 'linear' })
      ]
    });

    model.compile({
      optimizer: 'adam',
      loss: 'meanSquaredError',
      metrics: ['mae']
    });

    return model;
  }

  /**
   * 启动定期分析任务
   */
  private startPeriodicAnalysis(): void {
    // 每小时执行一次健康分析
    setInterval(async () => {
      await this.performPeriodicHealthAnalysis();
    }, 60 * 60 * 1000);

    this.logger.log('定期分析任务已启动');
  }

  /**
   * 执行定期健康分析
   */
  @Cron(CronExpression.EVERY_HOUR)
  private async performPeriodicHealthAnalysis(): Promise<void> {
    try {
      // 获取所有活跃设备列表
      const activeDevices = await this.getActiveDevices();

      for (const deviceId of activeDevices) {
        try {
          // 分析设备健康状态
          const healthScore = await this.analyzeDeviceHealth(deviceId);
          
          // 如果健康状态严重，进行故障预测
          if (healthScore.status === HealthStatus.CRITICAL || healthScore.status === HealthStatus.POOR) {
            await this.predictFailures(deviceId);
          }

        } catch (error) {
          this.logger.error(`设备 ${deviceId} 定期分析失败`, error);
        }
      }

      this.logger.debug('定期健康分析完成');

    } catch (error) {
      this.logger.error('定期健康分析失败', error);
    }
  }

  // 私有辅助方法
  private async getDeviceHealthData(deviceId: string, timeRange?: { start: Date; end: Date }): Promise<DeviceHealthData[]> {
    // 模拟设备健康数据
    const data: DeviceHealthData[] = [];
    const endDate = timeRange?.end || new Date();
    const startDate = timeRange?.start || moment(endDate).subtract(7, 'days').toDate();
    
    let current = moment(startDate);
    while (current.isBefore(endDate)) {
      data.push({
        deviceId,
        timestamp: current.toDate(),
        temperature: 25 + Math.random() * 20 + Math.sin(current.hour() / 24 * Math.PI) * 5,
        vibration: Math.random() * 5 + Math.sin(current.valueOf() / 1000000) * 2,
        pressure: 100 + Math.random() * 20,
        current: 5 + Math.random() * 3,
        voltage: 220 + Math.random() * 10,
        speed: 1500 + Math.random() * 500,
        torque: 50 + Math.random() * 20,
        efficiency: 85 + Math.random() * 10,
        operatingHours: current.diff(moment().subtract(1, 'year'), 'hours'),
        cycleCount: Math.floor(current.diff(moment().subtract(1, 'year'), 'hours') * 10)
      });
      current.add(1, 'hour');
    }
    
    return data;
  }

  private calculateComponentScores(healthData: DeviceHealthData[]): HealthScore['componentScores'] {
    const latest = healthData[healthData.length - 1];
    
    return {
      mechanical: this.calculateMechanicalScore(healthData),
      electrical: this.calculateElectricalScore(healthData),
      thermal: this.calculateThermalScore(healthData),
      vibration: this.calculateVibrationScore(healthData),
      performance: this.calculatePerformanceScore(healthData)
    };
  }

  private calculateMechanicalScore(data: DeviceHealthData[]): number {
    const latest = data[data.length - 1];
    const vibrationScore = Math.max(0, 100 - latest.vibration * 20);
    const torqueScore = Math.max(0, 100 - Math.abs(latest.torque - 60) * 2);
    return (vibrationScore + torqueScore) / 2;
  }

  private calculateElectricalScore(data: DeviceHealthData[]): number {
    const latest = data[data.length - 1];
    const currentScore = Math.max(0, 100 - Math.abs(latest.current - 6.5) * 10);
    const voltageScore = Math.max(0, 100 - Math.abs(latest.voltage - 220) * 2);
    return (currentScore + voltageScore) / 2;
  }

  private calculateThermalScore(data: DeviceHealthData[]): number {
    const latest = data[data.length - 1];
    return Math.max(0, 100 - Math.max(0, latest.temperature - 40) * 2);
  }

  private calculateVibrationScore(data: DeviceHealthData[]): number {
    const latest = data[data.length - 1];
    return Math.max(0, 100 - latest.vibration * 20);
  }

  private calculatePerformanceScore(data: DeviceHealthData[]): number {
    const latest = data[data.length - 1];
    return latest.efficiency;
  }

  private calculateOverallHealthScore(componentScores: HealthScore['componentScores']): number {
    const weights = {
      mechanical: 0.25,
      electrical: 0.2,
      thermal: 0.2,
      vibration: 0.15,
      performance: 0.2
    };

    return Object.entries(componentScores).reduce((total, [component, score]) => {
      return total + score * weights[component as keyof typeof weights];
    }, 0);
  }

  private determineHealthStatus(score: number): HealthStatus {
    if (score >= 90) return HealthStatus.EXCELLENT;
    if (score >= 80) return HealthStatus.GOOD;
    if (score >= 70) return HealthStatus.FAIR;
    if (score >= 60) return HealthStatus.POOR;
    return HealthStatus.CRITICAL;
  }

  private async analyzeTrends(deviceId: string, healthData: DeviceHealthData[]): Promise<HealthScore['trends']> {
    // 简化的趋势分析
    const recentData = healthData.slice(-24); // 最近24小时
    const olderData = healthData.slice(-48, -24); // 前24小时

    const recentAvg = ss.mean(recentData.map(d => d.efficiency));
    const olderAvg = ss.mean(olderData.map(d => d.efficiency));

    const shortTermTrend = recentAvg > olderAvg + 1 ? 'improving' : 
                          recentAvg < olderAvg - 1 ? 'degrading' : 'stable';

    // 长期趋势分析（简化）
    const longTermTrend = 'stable'; // 简化实现

    return {
      shortTerm: shortTermTrend,
      longTerm: longTermTrend
    };
  }

  private async runFailurePredictionModel(
    deviceId: string, 
    healthData: DeviceHealthData[], 
    horizon: number
  ): Promise<FailurePrediction[]> {
    // 简化的故障预测实现
    const predictions: FailurePrediction[] = [];
    const latest = healthData[healthData.length - 1];

    // 基于规则的简单预测
    if (latest.temperature > 60) {
      predictions.push({
        deviceId,
        failureType: FailureType.OVERHEATING,
        probability: Math.min(0.9, (latest.temperature - 40) / 40),
        timeToFailure: Math.max(1, 100 - latest.temperature),
        confidence: 0.8,
        contributingFactors: ['高温运行', '散热不良'],
        recommendedActions: ['检查冷却系统', '清洁散热器'],
        severity: latest.temperature > 70 ? 'high' : 'medium',
        timestamp: new Date()
      });
    }

    if (latest.vibration > 3) {
      predictions.push({
        deviceId,
        failureType: FailureType.MECHANICAL,
        probability: Math.min(0.9, latest.vibration / 5),
        timeToFailure: Math.max(1, (5 - latest.vibration) * 50),
        confidence: 0.75,
        contributingFactors: ['振动异常', '机械磨损'],
        recommendedActions: ['检查轴承', '平衡校正'],
        severity: latest.vibration > 4 ? 'high' : 'medium',
        timestamp: new Date()
      });
    }

    return predictions;
  }

  private async runRULPredictionModel(deviceId: string, healthData: DeviceHealthData[], component?: string): Promise<number> {
    // 简化的RUL计算
    const latest = healthData[healthData.length - 1];
    const baseRUL = 8760; // 1年基准
    
    // 基于健康状态调整RUL
    const healthFactor = latest.efficiency / 100;
    const temperatureFactor = Math.max(0.1, 1 - (latest.temperature - 25) / 100);
    const vibrationFactor = Math.max(0.1, 1 - latest.vibration / 10);
    
    const adjustedRUL = baseRUL * healthFactor * temperatureFactor * vibrationFactor;
    
    return Math.max(1, adjustedRUL);
  }

  private async runAnomalyDetectionModel(deviceId: string, healthData: DeviceHealthData[], sensitivity: number): Promise<any[]> {
    const anomalies: any[] = [];
    const latest = healthData[healthData.length - 1];
    
    // 基于统计的异常检测
    const metrics = ['temperature', 'vibration', 'pressure', 'current', 'voltage'];
    
    for (const metric of metrics) {
      const values = healthData.map(d => (d as any)[metric]);
      const mean = ss.mean(values);
      const stdDev = ss.standardDeviation(values);
      const threshold = mean + sensitivity * 2 * stdDev;
      
      const currentValue = (latest as any)[metric];
      if (currentValue > threshold) {
        anomalies.push({
          deviceId,
          metric,
          value: currentValue,
          threshold,
          severity: currentValue > threshold * 1.2 ? 'high' : 'medium',
          timestamp: latest.timestamp
        });
      }
    }
    
    return anomalies;
  }

  private generateCriticalMaintenanceRecommendations(deviceId: string, healthScore: HealthScore): MaintenanceRecommendation[] {
    const recommendations: MaintenanceRecommendation[] = [];
    
    if (healthScore.componentScores.thermal < 60) {
      recommendations.push({
        deviceId,
        maintenanceType: MaintenanceType.CORRECTIVE,
        priority: 'urgent',
        description: '设备过热，需要立即检查冷却系统',
        estimatedDuration: 4,
        estimatedCost: 2000,
        requiredSkills: ['机械维修', '冷却系统'],
        requiredParts: ['冷却液', '散热器'],
        scheduledDate: new Date(),
        deadline: moment().add(24, 'hours').toDate(),
        riskIfDeferred: '设备可能因过热而损坏'
      });
    }
    
    return recommendations;
  }

  private generatePredictiveMaintenanceRecommendations(prediction: FailurePrediction): MaintenanceRecommendation[] {
    const recommendations: MaintenanceRecommendation[] = [];
    
    recommendations.push({
      deviceId: prediction.deviceId,
      maintenanceType: MaintenanceType.PREDICTIVE,
      priority: prediction.severity === 'high' ? 'urgent' : 'high',
      description: `预防 ${prediction.failureType} 故障`,
      estimatedDuration: 6,
      estimatedCost: 3000,
      requiredSkills: ['预测性维护', '故障诊断'],
      requiredParts: ['备用零件'],
      scheduledDate: moment().add(prediction.timeToFailure * 0.8, 'hours').toDate(),
      deadline: moment().add(prediction.timeToFailure, 'hours').toDate(),
      riskIfDeferred: `${prediction.failureType} 故障风险: ${(prediction.probability * 100).toFixed(1)}%`
    });
    
    return recommendations;
  }

  private async generatePreventiveMaintenanceRecommendations(deviceId: string): Promise<MaintenanceRecommendation[]> {
    // 简化的预防性维护建议
    return [
      {
        deviceId,
        maintenanceType: MaintenanceType.PREVENTIVE,
        priority: 'medium',
        description: '定期保养和检查',
        estimatedDuration: 2,
        estimatedCost: 500,
        requiredSkills: ['常规维护'],
        requiredParts: ['润滑油', '滤芯'],
        scheduledDate: moment().add(7, 'days').toDate(),
        deadline: moment().add(14, 'days').toDate(),
        riskIfDeferred: '设备性能可能逐渐下降'
      }
    ];
  }

  private async getActiveDevices(): Promise<string[]> {
    // 模拟活跃设备列表
    return ['device-001', 'device-002', 'device-003'];
  }
}
